CURSOR VIP:CURSOR VIP
设备码:DeviceID
付费到期时间:Paid expiration time
推广命令：(已推广:Promotion command:(promoted
人,推广已付费: paid
人；每推广年付费2人可自动获得一年授权): '; each promotion year pays 2 people can automatically get a one-year authorization)'
专属推广链接:Exclusive promotion link
选择要授权的产品：:Choose the product to authorize:
请输入产品编号（直接回车默认为1，可以同时输入多个例如 145）：:Please enter the product number (press Enter directly to default to 1, you can enter multiple at the same time, such as 145):
输入有误:Input error
选择的产品为：:The selected product is:
选择有效期：:'Select validity period: '
小时(免费): ' hours (free)'
年(购买): ' year (purchase)'
请输入有效期编号（直接回车默认为1）：:Please enter the validity period number (press Enter directly to default to 1):
选择的有效期为：:The selected validity period is:
（已复制到剪贴板）:（Copied to clipboard）
付费已到期,捐赠以获取一年期授权: Payment has expired, donate to get a one-year authorization
捐赠完成后请回车:Please press Enter after donation
未捐赠,请捐赠完成后回车:Not donated, please press Enter after donation
经由:Via
代理访问:Proxy access
授权成功！使用过程请不要关闭此窗口:Authorization successful! Please do not close this window during use
此模式需要配置信任证书才能生效: This mode requires configuring a trusted certificate to take effect
请先按教程信任证书: Please trust the certificate according to the tutorial first
选择启动模式：:Select startup mode:
强劲代理模式:Strong proxy mode
极简模式:Minimalist mode
请输入模式编号（直接回车默认为1）：:Please enter the mode number (press Enter directly to default to 1):
有新版本，请关闭本窗口，将下面命令粘贴到GitBash窗口执行:There is a new version, please close this window and paste the following command into the GitBash window to execute
有新版本，请关闭本窗口，将下面命令粘贴到新终端窗口执行:There is a new version, please close this window and paste the following command into a new terminal window to execute
不支持容器环境: Container environment not supported
如果遇到问题请重启 cursor-vip: If issues arise,restart cursor-vip
Switch to English：Press 'sen' on keyboard in turn: 切换为中文：依次按键盘 szh
切换模式依次按键盘: Switch mode by pressing
Settings successful, will take effect after restart: 设置成功，将在重启 cursor-vip 后生效
设置成功，将在重启 cursor-vip 后生效: Settings successful, will take effect after restart cursor-vip
操作完成请重启cursor-vip: Please restart cursor-vip after the operation is completed
请输入电脑密码并回车用于首次配置证书: Please enter the computer password and press Enter to configure the certificate for the first time
请先安装Cursor客户端！！！: Please install the Cursor client first!!!
Cursor目录权限错误: Cursor directory permission error
请在管理员cmd中执行以下命令：: Please execute the following command in the administrator cmd:
或在管理员PowerShell中执行以下命令：: Or execute the following command in the administrator PowerShell:
请在新终端中执行以下命令：: Please execute the following command in a new terminal:
右键属性->取消只读的复选框->确定->确定: Right-click Properties->Uncheck the read-only checkbox->OK->OK
购买独享账号：依次按键盘 buy: Purchase an exclusive account: Press 'buy' on the keyboard in turn
独享账号已使用天数: Exclusive account days used
已购买独享账号,预计n小时内人工分配完成: Purchased an exclusive account, it is expected to be manually allocated within n hours
捐赠完成后请依次按键 ckp: Please press 'ckp' in turn after donation is completed
捐赠完成后请依次按键 c3p: Please press 'c3p' in turn after donation is completed
当前模式: Current mode
查询账号自动刷新剩余天数：依次按键盘 q3d: Query the remaining days of automatic refresh of the account: Press 'q3d' on the keyboard in turn
切换老账号：依次按键盘 u3o: Switch to old account: Press 'u3o' on the keyboard in turn
小额付费刷新账号：依次按键盘 u3d: Pay to refresh the account: Press 'u3d' on the keyboard in turn
小额付费刷新账号：依次按键盘 u3t: Pay to refresh the account: Press 'u3t' on the keyboard in turn
小额付费刷新账号：依次按键盘 u3h: Pay to refresh the account: Press 'u3h' on the keyboard in turn
购买成功，将在重启 cursor-vip 后生效: Purchase successful, will take effect after restart cursor-vip
订阅时长会在验证通过后增加对应的天数: The subscription duration will increase the corresponding number of days after the verification is passed
免付刷新次数: Free refresh times
抱歉，模式三暂无新账号，请稍后再试: Sorry, there are no new accounts in mod 3, please try again later
模式三暂无新账号，将自动切换到模式二: There are no new accounts in mod 3, and it will automatically switch to mod 2



